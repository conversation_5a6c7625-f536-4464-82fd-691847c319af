{"version": "5", "remote": {"https://deno.land/std@0.99.0/_util/assert.ts": "2f868145a042a11d5ad0a3c748dcf580add8a0dbc0e876eaa0026303a5488f58", "https://deno.land/std@0.99.0/_util/os.ts": "e282950a0eaa96760c0cf11e7463e66babd15ec9157d4c9ed49cc0925686f6a7", "https://deno.land/std@0.99.0/path/_constants.ts": "1247fee4a79b70c89f23499691ef169b41b6ccf01887a0abd131009c5581b853", "https://deno.land/std@0.99.0/path/_interface.ts": "1fa73b02aaa24867e481a48492b44f2598cd9dfa513c7b34001437007d3642e4", "https://deno.land/std@0.99.0/path/_util.ts": "2e06a3b9e79beaf62687196bd4b60a4c391d862cfa007a20fc3a39f778ba073b", "https://deno.land/std@0.99.0/path/common.ts": "eaf03d08b569e8a87e674e4e265e099f237472b6fd135b3cbeae5827035ea14a", "https://deno.land/std@0.99.0/path/glob.ts": "314ad9ff263b895795208cdd4d5e35a44618ca3c6dd155e226fb15d065008652", "https://deno.land/std@0.99.0/path/mod.ts": "4465dc494f271b02569edbb4a18d727063b5dbd6ed84283ff906260970a15d12", "https://deno.land/std@0.99.0/path/posix.ts": "f56c3c99feb47f30a40ce9d252ef6f00296fa7c0fcb6dd81211bdb3b8b99ca3b", "https://deno.land/std@0.99.0/path/separator.ts": "8fdcf289b1b76fd726a508f57d3370ca029ae6976fcde5044007f062e643ff1c", "https://deno.land/std@0.99.0/path/win32.ts": "77f7b3604e0de40f3a7c698e8a79e7f601dc187035a1c21cb1e596666ce112f8", "https://deno.land/x/media_types@v2.9.0/db.ts": "ba39cddbcefce47d577c0529066787a3a7b39d27750a6d32b5ad53ed487e7b7b", "https://deno.land/x/media_types@v2.9.0/deps.ts": "364b24c35845cfd5c6903ab22b8ba9873bf1022bbbf6bf3d001695332d4bbb4f", "https://deno.land/x/media_types@v2.9.0/mod.ts": "d63583b978d32eff8b76e1ae5d83cba2fb27baa90cc1bcb0ad15a06122ea8c19", "https://deno.land/x/xhr@0.1.0/mod.ts": "5200325d879e571961f0927e8e32e66fd33f4ba0d29a219560cf9e0fe9bc6cdf"}, "workspace": {"packageJson": {"dependencies": ["npm:@eslint/js@^9.9.0", "npm:@hookform/resolvers@^3.9.0", "npm:@radix-ui/react-accordion@^1.2.0", "npm:@radix-ui/react-alert-dialog@^1.1.1", "npm:@radix-ui/react-aspect-ratio@^1.1.0", "npm:@radix-ui/react-avatar@^1.1.0", "npm:@radix-ui/react-checkbox@^1.1.1", "npm:@radix-ui/react-collapsible@^1.1.0", "npm:@radix-ui/react-context-menu@^2.2.1", "npm:@radix-ui/react-dialog@^1.1.2", "npm:@radix-ui/react-dropdown-menu@^2.1.1", "npm:@radix-ui/react-hover-card@^1.1.1", "npm:@radix-ui/react-label@^2.1.0", "npm:@radix-ui/react-menubar@^1.1.1", "npm:@radix-ui/react-navigation-menu@^1.2.0", "npm:@radix-ui/react-popover@^1.1.1", "npm:@radix-ui/react-progress@^1.1.0", "npm:@radix-ui/react-radio-group@^1.2.0", "npm:@radix-ui/react-scroll-area@^1.1.0", "npm:@radix-ui/react-select@^2.1.1", "npm:@radix-ui/react-separator@^1.1.0", "npm:@radix-ui/react-slider@^1.2.0", "npm:@radix-ui/react-slot@^1.1.0", "npm:@radix-ui/react-switch@^1.1.0", "npm:@radix-ui/react-tabs@^1.1.0", "npm:@radix-ui/react-toast@^1.2.1", "npm:@radix-ui/react-toggle-group@^1.1.0", "npm:@radix-ui/react-toggle@^1.1.0", "npm:@radix-ui/react-tooltip@^1.1.4", "npm:@supabase/supabase-js@^2.50.3", "npm:@tailwindcss/typography@~0.5.15", "npm:@tanstack/react-query@^5.56.2", "npm:@types/node@^22.5.5", "npm:@types/react-dom@^18.3.0", "npm:@types/react@^18.3.3", "npm:@vitejs/plugin-react-swc@^3.5.0", "npm:autoprefixer@^10.4.20", "npm:class-variance-authority@~0.7.1", "npm:clsx@^2.1.1", "npm:cmdk@1", "npm:date-fns@^3.6.0", "npm:docx@^8.5.0", "npm:embla-carousel-react@^8.3.0", "npm:eslint-plugin-react-hooks@^5.1.0-rc.0", "npm:eslint-plugin-react-refresh@~0.4.9", "npm:eslint@^9.9.0", "npm:framer-motion@^12.23.0", "npm:globals@^15.9.0", "npm:input-otp@^1.2.4", "npm:lovable-tagger@^1.1.7", "npm:lucide-react@0.462", "npm:next-themes@0.3", "npm:postcss@^8.4.47", "npm:react-day-picker@^8.10.1", "npm:react-dom@^18.3.1", "npm:react-hook-form@^7.53.0", "npm:react-resizable-panels@^2.1.3", "npm:react-router-dom@^6.26.2", "npm:react@^18.3.1", "npm:recharts@^2.12.7", "npm:sonner@^1.5.0", "npm:tailwind-merge@^2.5.2", "npm:tailwindcss-animate@^1.0.7", "npm:tailwindcss@^3.4.11", "npm:typescript-eslint@^8.0.1", "npm:typescript@^5.5.3", "npm:vaul@~0.9.3", "npm:vite@^5.4.1", "npm:zod@^3.23.8"]}}}