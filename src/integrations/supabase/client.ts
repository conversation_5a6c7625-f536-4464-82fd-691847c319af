// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://iunowowqpjuzwfmsserk.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml1bm93b3dxcGp1endmbXNzZXJrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNjQ3NjcsImV4cCI6MjA2Nzg0MDc2N30.Yq8KuepSSURgxgDoOpKD20Ew92GFRq2T2HYWW-iQ8HA";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});