import { Link } from "react-router-dom";
import { Building } from "lucide-react";

const About = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="max-w-none mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Building className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">DraftFlo</span>
            </Link>
            
            <div className="hidden md:flex items-center gap-8">
              <Link to="/#features" className="text-slate-300 hover:text-white transition-colors">Features</Link>
              <Link to="/about" className="text-white font-medium">About</Link>
              <Link to="/security" className="text-slate-300 hover:text-white transition-colors">Security</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* About Section */}
      <section className="py-24 pt-32">
        <div className="max-w-none mx-auto px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-8">
              About Us
            </h1>
            
            <div className="space-y-6 text-lg text-slate-300 leading-relaxed">
              <p>
                We're building the fastest way to turn real estate deals into investment memos.
              </p>
              
              <p>
                After years of watching analysts and deal teams spend hours rewriting the same IC memos, formatting charts, and chasing down Excel outputs, we realized: the memo process is broken.
              </p>
              
              <p>
                Our mission is to eliminate the manual grind of memo creation — and replace it with AI-driven tools built specifically for real estate investment professionals.
              </p>
              
              <p>
                Whether you're at a REPE firm, syndicating deals, or developing ground-up projects, our platform helps you move faster, present better, and stay focused on what really matters: closing the right deals.
              </p>
              
              <p className="text-xl font-semibold text-blue-400">
                This isn't generic AI — it's trained on how real investors talk, think, and make decisions.
              </p>
              
              <p>
                We're currently in private beta. Join us early and help shape the future of how real estate deals get done.
              </p>
            </div>

            <div className="mt-12">
              <Link 
                to="/" 
                className="inline-flex items-center justify-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
              >
                Join Our Waitlist
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;