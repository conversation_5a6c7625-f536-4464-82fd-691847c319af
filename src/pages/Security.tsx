import { Link } from "react-router-dom";
import { Building, Shield, Lock, Key, Database, FileCheck, Eye } from "lucide-react";

const Security = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="max-w-none mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Building className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">DraftFlo</span>
            </Link>
            
            <div className="hidden md:flex items-center gap-8">
              <Link to="/#features" className="text-slate-300 hover:text-white transition-colors">Features</Link>
              <Link to="/about" className="text-slate-300 hover:text-white transition-colors">About</Link>
              <Link to="/security" className="text-white font-medium">Security</Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Security Section */}
      <section className="py-24 pt-32">
        <div className="max-w-none mx-auto px-8">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 text-center">
              Security & Privacy
            </h1>
            
            <div className="space-y-6 text-lg text-slate-300 leading-relaxed mb-8">
              <p>
                At DraftFlo, protecting your sensitive deal data will be our highest priority.
              </p>
              
              <p>
                As we finish building the product beyond beta, we aim to implement industry-leading security practices to keep your information safe and confidential, including:
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-8">
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-3">
                  <Lock className="h-6 w-6 text-blue-400" />
                  <h3 className="text-xl font-semibold text-white">Encryption in transit and at rest</h3>
                </div>
                <p className="text-slate-300">All data uploaded will be protected with TLS during transfer and AES-256 while stored.</p>
              </div>
              
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-3">
                  <Shield className="h-6 w-6 text-blue-400" />
                  <h3 className="text-xl font-semibold text-white">Enterprise-grade AI integration</h3>
                </div>
                <p className="text-slate-300">We will use Claude's secure enterprise API, which does not retain or use your data for model training.</p>
              </div>
              
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-3">
                  <Key className="h-6 w-6 text-blue-400" />
                  <h3 className="text-xl font-semibold text-white">Role-based access control</h3>
                </div>
                <p className="text-slate-300">Only authorized team members will have access to your deal documents and memos.</p>
              </div>
              
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-3">
                  <Database className="h-6 w-6 text-blue-400" />
                  <h3 className="text-xl font-semibold text-white">Data retention & deletion policies</h3>
                </div>
                <p className="text-slate-300">You will have full control over your data, including permanent deletion on demand or automatic deletion after a set period.</p>
              </div>
              
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-3">
                  <FileCheck className="h-6 w-6 text-blue-400" />
                  <h3 className="text-xl font-semibold text-white">Compliance readiness</h3>
                </div>
                <p className="text-slate-300">We are working toward SOC 2 certification and aligning with GDPR and CCPA standards.</p>
              </div>
              
              <div className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-3">
                  <Eye className="h-6 w-6 text-blue-400" />
                  <h3 className="text-xl font-semibold text-white">Ongoing security improvements</h3>
                </div>
                <p className="text-slate-300">We plan to continuously monitor and enhance our systems to protect against evolving threats.</p>
              </div>
            </div>
            
            <div className="space-y-6 text-lg text-slate-300 leading-relaxed">
              <p className="text-center">
                Security is fundamental to our mission. While some measures are still being finalized during beta, transparency and trust remain top priorities.
              </p>
              
              <p className="text-center text-blue-400">
                If you have any questions about our approach to security, please reach out to our support team.
              </p>
            </div>

            <div className="mt-12 text-center">
              <Link 
                to="/" 
                className="inline-flex items-center justify-center px-8 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors"
              >
                Join Our Waitlist
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Security;