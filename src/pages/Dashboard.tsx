import { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Upload, FileText, BarChart3, Building, FileSpreadsheet, LogOut, User, Download, Loader2, Handshake, Landmark, FileCheck2, Image as ImageIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import FileList from "@/components/FileList";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface FolderItem {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  fileCount: number;
  color: string;
}

const Dashboard = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedFolder, setSelectedFolder] = useState<FolderItem | null>(null);
  const [isGeneratingMemo, setIsGeneratingMemo] = useState(false);
  const [dragOverFolder, setDragOverFolder] = useState<string | null>(null);
  const [recentFiles, setRecentFiles] = useState<Array<{
    id: string;
    name: string;
    folder_id: string;
    created_at: string;
  }>>([]);
  const [folders, setFolders] = useState<FolderItem[]>([
    {
      id: "financial",
      name: "Financial Documents",
      description: "Excel files, financial statements, historical data",
      icon: FileSpreadsheet,
      fileCount: 0,
      color: "bg-green-50 border-green-200 hover:bg-green-100"
    },
    {
      id: "property",
      name: "Property Analysis",
      description: "Argus files, appraisals, property reports",
      icon: Building,
      fileCount: 0,
      color: "bg-blue-50 border-blue-200 hover:bg-blue-100"
    },
    {
      id: "market",
      name: "Market Research",
      description: "Market reports, comparables, industry analysis",
      icon: BarChart3,
      fileCount: 0,
      color: "bg-purple-50 border-purple-200 hover:bg-purple-100"
    },
    {
      id: "legal",
      name: "Legal Documents",
      description: "Contracts, due diligence, legal reports",
      icon: FileText,
      fileCount: 0,
      color: "bg-amber-50 border-amber-200 hover:bg-amber-100"
    },
    {
      id: "partner_terms",
      name: "Partner Terms",
      description: "Term sheets and partnership agreements",
      icon: Handshake,
      fileCount: 0,
      color: "bg-pink-50 border-pink-200 hover:bg-pink-100"
    },
    {
      id: "debt",
      name: "Debt",
      description: "Debt terms, loan agreements, lender documents",
      icon: Landmark,
      fileCount: 0,
      color: "bg-red-50 border-red-200 hover:bg-red-100"
    },
    {
      id: "third_party_reports",
      name: "Third Party Reports",
      description: "Property Condition Reports, Appraisals, Environmental Reports",
      icon: FileCheck2,
      fileCount: 0,
      color: "bg-cyan-50 border-cyan-200 hover:bg-cyan-100"
    },
    {
      id: "property_photos",
      name: "Property Photos",
      description: "Photos and images of the property",
      icon: ImageIcon,
      fileCount: 0,
      color: "bg-yellow-50 border-yellow-200 hover:bg-yellow-100"
    }
  ]);
  const fileInputRefs = useRef<{[key: string]: HTMLInputElement | null}>({});

  // Fetch file counts for all folders
  const fetchFileCounts = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const folderIds = ["financial", "property", "market", "legal", "partner_terms", "debt", "third_party_reports", "property_photos"];
      const { data: files } = await supabase
        .from('files')
        .select('folder_id')
        .eq('user_id', user.id)
        .in('folder_id', folderIds);

      if (files) {
        const counts = files.reduce((acc: {[key: string]: number}, file) => {
          acc[file.folder_id] = (acc[file.folder_id] || 0) + 1;
          return acc;
        }, {});

        setFolders(prev => prev.map(folder => ({
          ...folder,
          fileCount: counts[folder.id] || 0
        })));
      }
    } catch (error) {
      console.error('Error fetching file counts:', error);
    }
  };

  // Fetch recent files
  const fetchRecentFiles = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { data: files } = await supabase
        .from('files')
        .select('id, name, folder_id, created_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(5);

      setRecentFiles(files || []);
    } catch (error) {
      console.error('Error fetching recent files:', error);
    }
  };

  // Set up real-time subscription for file changes
  useEffect(() => {
    fetchFileCounts();
    fetchRecentFiles();

    const channel = supabase
      .channel('file_changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'files'
        },
        () => {
          fetchFileCounts();
          fetchRecentFiles();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, []);

  // Refresh counts when returning from FileList
  useEffect(() => {
    if (!selectedFolder) {
      fetchFileCounts();
      fetchRecentFiles();
    }
  }, [selectedFolder]);

  const handleFileUpload = async (folderId: string, files: FileList) => {
    if (!files.length) return;

    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast({
          title: "Authentication required",
          description: "Please log in to upload files",
          variant: "destructive",
        });
        return;
      }

      for (const file of Array.from(files)) {
        const fileName = `${user.id}/${Date.now()}-${file.name}`;
        
        // Upload file to storage
        const { error: uploadError } = await supabase.storage
          .from('documents')
          .upload(fileName, file);

        if (uploadError) throw uploadError;
        
        // Store file metadata in database
        const { error: dbError } = await supabase
          .from('files')
          .insert({
            user_id: user.id,
            folder_id: folderId,
            name: file.name,
            file_path: fileName,
            file_size: file.size,
            file_type: file.type
          });

        if (dbError) throw dbError;
      }

      // File counts will be updated via real-time subscription

      toast({
        title: "Files uploaded",
        description: `${files.length} file(s) uploaded successfully`,
      });
    } catch (error) {
      toast({
        title: "Upload failed",
        description: "Failed to upload files",
        variant: "destructive",
      });
    }
  };

  const handleFolderUpload = (folderId: string) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = '.pdf,.doc,.docx,.xls,.xlsx,.txt';
    input.onchange = (e) => {
      const target = e.target as HTMLInputElement;
      if (target.files) {
        handleFileUpload(folderId, target.files);
      }
    };
    input.click();
  };

  const generateMemo = async () => {
    setIsGeneratingMemo(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast({
          title: "Authentication required",
          description: "Please log in to generate memos",
          variant: "destructive",
        });
        return;
      }

      // Check if user has uploaded files
      const { data: files } = await supabase
        .from('files')
        .select('*')
        .eq('user_id', user.id);

      if (!files || files.length === 0) {
        toast({
          title: "No files found",
          description: "Please upload documents to generate an investment memo",
          variant: "destructive",
        });
        return;
      }

      // Call edge function to generate memo
      const { data, error } = await supabase.functions.invoke('generate-investment-memo', {
        body: { userId: user.id }
      });

      if (error) throw error;

      // Download the generated Word document
      const blob = new Blob([new Uint8Array(data.documentBuffer)], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Investment_Memo_${new Date().toLocaleDateString()}.docx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Memo generated",
        description: "Your investment memo has been generated and downloaded",
      });
    } catch (error) {
      toast({
        title: "Generation failed",
        description: "Failed to generate investment memo",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingMemo(false);
    }
  };

  const handleFolderClick = (folder: FolderItem) => {
    setSelectedFolder(folder);
  };

  const handleBackToFolders = () => {
    setSelectedFolder(null);
  };

  const handleDragOver = (e: React.DragEvent, folderId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOverFolder(folderId);
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e: React.DragEvent, folderId: string) => {
    e.preventDefault();
    e.stopPropagation();
    // Only clear drag state if leaving the entire drop zone
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setDragOverFolder(null);
    }
  };

  const handleDrop = (e: React.DragEvent, folderId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOverFolder(null);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileUpload(folderId, files);
    }
  };

  const getFolderName = (folderId: string) => {
    const folder = folders.find(f => f.id === folderId);
    return folder ? folder.name : folderId;
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const past = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - past.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return past.toLocaleDateString();
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleSignOut = async () => {
    try {
      // Clean up auth state
      Object.keys(localStorage).forEach((key) => {
        if (key.startsWith('supabase.auth.') || key.includes('sb-')) {
          localStorage.removeItem(key);
        }
      });

      // Sign out from Supabase
      await supabase.auth.signOut({ scope: 'global' });
      
      // Redirect to login page
      navigate('/login');
      
      toast({
        title: "Signed out",
        description: "You have been signed out successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sign out",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted">
      {/* Header */}
      <header className="bg-card border-b shadow-[var(--shadow-card)] sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-primary">Investment Memo AI</h1>
            <p className="text-sm text-muted-foreground">Real Estate Investment Platform</p>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full">
                <User className="h-5 w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-card border shadow-[var(--shadow-card)]">
              <DropdownMenuItem className="hover:bg-muted" onClick={() => navigate('/profile')}>
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-muted text-destructive" onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {selectedFolder ? (
          <FileList
            folderId={selectedFolder.id}
            folderName={selectedFolder.name}
            onBack={handleBackToFolders}
          />
        ) : (
          <>
            {/* Welcome Section */}
            <div className="mb-8">
              <h2 className="text-3xl font-bold text-foreground mb-2">
                Welcome to Your Investment Platform
              </h2>
              <p className="text-lg text-muted-foreground">
                Upload your documents to get started with AI-generated investment memos
              </p>
            </div>

            {/* Action Bar */}
            <div className="mb-8 flex flex-wrap gap-4">
              <Button 
                variant="professional" 
                size="lg" 
                className="gap-2"
                onClick={generateMemo}
                disabled={isGeneratingMemo}
              >
                {isGeneratingMemo ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Download className="h-5 w-5" />
                )}
                {isGeneratingMemo ? 'Generating...' : 'Generate Investment Memo'}
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                <BarChart3 className="h-5 w-5" />
                View Analytics
              </Button>
            </div>

            {/* Document Folders Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {folders.map((folder) => {
                const IconComponent = folder.icon;
                return (
                  <Card 
                    key={folder.id} 
                    className={`${folder.color} transition-[var(--transition-smooth)] cursor-pointer group border-2 shadow-[var(--shadow-card)] hover:shadow-[var(--shadow-elegant)]`}
                    onClick={() => handleFolderClick(folder)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <IconComponent className="h-8 w-8 text-primary" />
                        <div className="text-right">
                          <div className="text-2xl font-bold text-primary">{folder.fileCount}</div>
                          <div className="text-xs text-muted-foreground">files</div>
                        </div>
                      </div>
                      <CardTitle className="text-lg font-semibold text-foreground">
                        {folder.name}
                      </CardTitle>
                      <CardDescription className="text-sm">
                        {folder.description}
                      </CardDescription>
                    </CardHeader>
                     <CardContent className="pt-0">
                       <div 
                         className={`flex items-center justify-center p-8 border-2 border-dashed rounded-lg bg-background/50 transition-[var(--transition-smooth)] cursor-pointer ${
                           dragOverFolder === folder.id 
                             ? 'border-primary bg-primary/10' 
                             : 'border-primary/30 group-hover:border-primary/60'
                         }`}
                         onClick={(e) => {
                           e.stopPropagation();
                           handleFolderUpload(folder.id);
                         }}
                         onDragOver={(e) => handleDragOver(e, folder.id)}
                         onDragEnter={handleDragEnter}
                         onDragLeave={(e) => handleDragLeave(e, folder.id)}
                         onDrop={(e) => handleDrop(e, folder.id)}
                       >
                         <div className="text-center">
                           <Upload className="h-12 w-12 text-primary/60 mx-auto mb-2" />
                           <p className="text-sm font-medium text-primary">
                             Click to Upload
                           </p>
                           <p className="text-xs text-muted-foreground">
                             or drag and drop
                           </p>
                         </div>
                       </div>
                     </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Recent Activity */}
            <div className="mt-12">
              <h3 className="text-xl font-semibold text-foreground mb-6">Recent Activity</h3>
              <Card className="shadow-[var(--shadow-card)]">
                {recentFiles.length > 0 ? (
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {recentFiles.map((file) => (
                        <div 
                          key={file.id} 
                          className="flex items-center justify-between p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-[var(--transition-smooth)]"
                        >
                          <div className="flex items-center gap-3">
                            <FileText className="h-5 w-5 text-primary" />
                            <div>
                              <p className="font-medium text-foreground">{file.name}</p>
                              <p className="text-sm text-muted-foreground">
                                Uploaded to {getFolderName(file.folder_id)}
                              </p>
                            </div>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {formatDateTime(file.created_at)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                ) : (
                  <CardContent className="p-8 text-center">
                    <div className="text-muted-foreground">
                      <FileText className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <h4 className="text-lg font-medium mb-2">No documents uploaded yet</h4>
                      <p className="text-sm">
                        Start by uploading your first document to one of the folders above.
                      </p>
                    </div>
                  </CardContent>
                )}
              </Card>
            </div>
          </>
        )}
      </main>
    </div>
  );
};

export default Dashboard;