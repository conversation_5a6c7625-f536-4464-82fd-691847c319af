import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { She<PERSON>, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { 
  Building, 
  FileText, 
  BarChart3, 
  Shield, 
  ArrowRight, 
  Sparkles,
  Clock,
  Users,
  TrendingUp,
  Star,
  CheckCircle,
  Mail,
  Menu
} from "lucide-react";

const Index = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleWaitlistSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);
    
    try {
      const { error } = await supabase
        .from('waitlist')
        .insert([{ email }]);

      if (error) {
        // Handle duplicate email case
        if (error.code === '23505') {
          toast({
            title: "Already on the list!",
            description: "This email is already registered for our waitlist.",
            variant: "destructive",
          });
        } else {
          throw error;
        }
      } else {
        setIsSubmitted(true);
        setEmail("");
        toast({
          title: "Success!",
          description: "You've been added to our waitlist. We'll be in touch soon!",
        });
      }
    } catch (error) {
      console.error('Error adding to waitlist:', error);
      toast({
        title: "Something went wrong",
        description: "Please try again later or contact support.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    {
      icon: FileText,
      title: "AI-Generated Memos",
      description: "Create comprehensive investment committee memorandums with AI assistance"
    },
    {
      icon: Building,
      title: "Real Estate Focus", 
      description: "Specialized for real estate acquisition analysis and reporting"
    },
    {
      icon: BarChart3,
      title: "Data Analysis",
      description: "Upload financial data, market research, and property analysis for insights"
    },
    {
      icon: Shield,
      title: "Professional Platform",
      description: "Secure, enterprise-grade platform for investment professionals"
    }
  ];

  const testimonials = [
    {
      quote: "This platform gives me my time back. That's the most valuable thing it could do. I spend less time on menial tasks, and more time doing what I love – helping people close deals.",
      name: "Sarah Foley"
    },
    {
      quote: "The AI-powered analysis makes high-quality investment memos an afterthought. It allows our team to pay 100% of their attention to the clients and deals.",
      name: "Michael Rodriguez"
    },
    {
      quote: "This has fundamentally changed how we operate. Our team is better prepared, faster to market, and able to focus more on closing deals than building decks.",
      name: "Jennifer Park"
    }
  ];

  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 50 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.8 }
    }
  };

  const slideInLeft = {
    hidden: { opacity: 0, x: -100 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.8 }
    }
  };

  const slideInRight = {
    hidden: { opacity: 0, x: 100 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { duration: 0.8 }
    }
  };

  const rotateIn = {
    hidden: { opacity: 0, rotateY: 180 },
    visible: { 
      opacity: 1, 
      rotateY: 0,
      transition: { duration: 1 }
    }
  };

  const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const letterVariants = {
    hidden: { opacity: 0, y: 50, rotateX: -90 },
    visible: { 
      opacity: 1, 
      y: 0, 
      rotateX: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="max-w-none mx-auto px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Building className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-white">DraftFlo</span>
            </div>
            
            <div className="hidden md:flex items-center gap-8">
              <a href="#features" className="text-slate-300 hover:text-white transition-colors">Features</a>
              <Link to="/about" className="text-slate-300 hover:text-white transition-colors">About</Link>
              <Link to="/security" className="text-slate-300 hover:text-white transition-colors">Security</Link>
            </div>
            
            {/* Mobile menu */}
            <div className="md:hidden">
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" className="text-white hover:bg-slate-800">
                    <Menu className="h-6 w-6" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="bg-slate-900 border-slate-700">
                  <div className="flex flex-col gap-6 mt-8">
                    <Link 
                      to="/about" 
                      className="text-slate-300 hover:text-white transition-colors text-lg"
                    >
                      About
                    </Link>
                    <Link 
                      to="/security" 
                      className="text-slate-300 hover:text-white transition-colors text-lg"
                    >
                      Security
                    </Link>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-20">
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute top-3/4 left-3/4 w-32 h-32 bg-cyan-500/10 rounded-full blur-2xl animate-pulse delay-500"></div>
        </div>

        <motion.div 
          className="relative z-10 max-w-none mx-auto px-8"
          initial="hidden"
          animate="visible"
          variants={staggerContainer}
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Content */}
            <motion.div className="text-left" variants={slideInLeft}>
              <motion.div 
                className="inline-flex items-center gap-2 bg-blue-600/10 text-blue-400 rounded-full px-4 py-2 mb-6 border border-blue-600/20"
                variants={fadeInUp}
              >
                <Sparkles className="h-4 w-4" />
                <span className="text-sm font-medium">Coming Soon</span>
              </motion.div>

              <motion.h1 
                className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight"
                variants={staggerContainer}
                initial="hidden"
                animate="visible"
              >
                <motion.span variants={letterVariants}>The Future of</motion.span>
                <br />
                <motion.span 
                  className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500"
                  variants={rotateIn}
                >
                  Real Estate IC Memos
                </motion.span>
              </motion.h1>
              
              <motion.p 
                className="text-xl text-slate-300 mb-8 leading-relaxed max-w-2xl"
                variants={slideInLeft}
              >
                Generate polished, professional investment memos in minutes — not hours. Upload your pro forma and deal docs, and let AI handle the rest. Join the waitlist for early access.
              </motion.p>
              
              {/* Waitlist Form */}
              <motion.div variants={fadeInUp}>
                {!isSubmitted ? (
                  <form onSubmit={handleWaitlistSubmit} className="mb-8">
                    <div className="flex flex-col sm:flex-row gap-4 max-w-md">
                      <div className="flex-1">
                        <Input
                          type="email"
                          placeholder="Enter your email address"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="bg-slate-800 border-slate-600 text-white placeholder:text-slate-400 h-12 text-lg"
                          required
                        />
                      </div>
                      <Button 
                        type="submit"
                        size="lg" 
                        className="bg-blue-600 hover:bg-blue-700 text-white px-8 h-12 text-lg font-semibold"
                        disabled={isLoading}
                      >
                        {isLoading ? "Adding..." : "Join Waitlist"}
                        <Mail className="ml-2 h-5 w-5" />
                      </Button>
                    </div>
                  </form>
                ) : (
                  <div className="bg-green-600/10 border border-green-600/20 rounded-lg p-6 mb-8 max-w-md">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-6 w-6 text-green-500" />
                      <div>
                        <p className="text-green-400 font-semibold">You're on the list!</p>
                        <p className="text-slate-300 text-sm">We'll notify you when DraftFlo launches.</p>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>

              {/* Trust indicators */}
              <motion.div 
                className="flex flex-wrap items-center gap-6 text-sm text-slate-400"
                variants={staggerContainer}
              >
                <motion.div className="flex items-center gap-2" variants={fadeInUp}>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Early Access Guaranteed</span>
                </motion.div>
                <motion.div className="flex items-center gap-2" variants={fadeInUp}>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>No Spam, Ever</span>
                </motion.div>
                <motion.div className="flex items-center gap-2" variants={fadeInUp}>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>Exclusive Beta Access</span>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Right side - Visual element */}
            <motion.div 
              className="relative"
              variants={slideInRight}
            >
              <div className="relative bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 border border-slate-700/50 shadow-2xl">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-2xl"></div>
                
                <div className="relative">
                  <div className="flex items-center gap-2 mb-6">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <FileText className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="h-2 bg-slate-600 rounded w-48 mb-1"></div>
                        <div className="h-2 bg-slate-700 rounded w-32"></div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-purple-600 rounded-lg flex items-center justify-center">
                        <BarChart3 className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="h-2 bg-slate-600 rounded w-56 mb-1"></div>
                        <div className="h-2 bg-slate-700 rounded w-40"></div>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-cyan-600 rounded-lg flex items-center justify-center">
                        <TrendingUp className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <div className="h-2 bg-slate-600 rounded w-44 mb-1"></div>
                        <div className="h-2 bg-slate-700 rounded w-36"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-24 bg-slate-900/50">
        <div className="max-w-none mx-auto px-8">
          <motion.div 
            className="text-center mb-20"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Smarter, Better, Faster Deals
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Streamline your investment process with AI-powered analysis and memo generation
            </p>
          </motion.div>

          <motion.div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={staggerContainer}
          >
            <motion.div className="text-center" variants={slideInLeft}>
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Clock className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">Unmatched Speed</h3>
              <p className="text-slate-300">Nobody wants to wait weeks for investment committee memos. With our AI platform, get it done in hours.</p>
            </motion.div>
            
            <motion.div className="text-center" variants={fadeInUp}>
              <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">Expert Quality</h3>
              <p className="text-slate-300">Professional-grade analysis that matches the quality of senior analysts.</p>
            </motion.div>
            
            <motion.div className="text-center" variants={slideInRight}>
              <div className="w-16 h-16 bg-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-2">Better Outcomes</h3>
              <p className="text-slate-300">Focus on closing deals instead of building decks. Win more of the right deals.</p>
            </motion.div>
          </motion.div>

          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={staggerContainer}
          >
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div 
                  key={index}
                  className="bg-slate-800/50 rounded-xl p-6 border border-slate-700/50 hover:border-blue-500/50 transition-all duration-300"
                  variants={index % 2 === 0 ? slideInLeft : slideInRight}
                  whileHover={{ scale: 1.05, rotateY: 5 }}
                  transition={{ duration: 0.3 }}
                >
                  <motion.div 
                    className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-4"
                    whileHover={{ rotateZ: 360 }}
                    transition={{ duration: 0.5 }}
                  >
                    <IconComponent className="h-6 w-6 text-white" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-slate-300 text-sm">
                    {feature.description}
                  </p>
                </motion.div>
              );
            })}
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-24 bg-slate-800/30">
        <div className="max-w-none mx-auto px-8">
          <motion.div 
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              What Our Users Say
            </h2>
            <p className="text-xl text-slate-300">
              Trusted by investment professionals worldwide
            </p>
          </motion.div>

          <motion.div 
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, amount: 0.3 }}
            variants={staggerContainer}
          >
            {testimonials.map((testimonial, index) => (
              <motion.div 
                key={index} 
                className="bg-slate-800/50 rounded-xl p-8 border border-slate-700/50 flex flex-col justify-between" 
                variants={index === 1 ? fadeInUp : index === 0 ? slideInLeft : slideInRight}
                whileHover={{ 
                  scale: 1.02, 
                  rotateY: 2,
                  boxShadow: "0 20px 40px rgba(0,0,0,0.3)"
                }}
                transition={{ duration: 0.3 }}
              >
                <div>
                  <motion.div 
                    className="flex items-center gap-1 mb-4"
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ delay: 0.5, duration: 0.5 }}
                  >
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                    ))}
                  </motion.div>
                  <p className="text-slate-300 mb-6 italic">"{testimonial.quote}"</p>
                </div>
                <div className="mt-auto">
                  <p className="text-white font-semibold">{testimonial.name}</p>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <motion.section 
        className="py-24 bg-gradient-to-r from-blue-600 to-purple-600"
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.3 }}
        variants={fadeInUp}
      >
        <div className="max-w-none mx-auto text-center px-8">
          <motion.h2 
            className="text-4xl md:text-5xl font-bold text-white mb-6"
            variants={slideInLeft}
          >
            Don't Miss Out on the Future
          </motion.h2>
          
          <motion.p 
            className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto"
            variants={slideInRight}
          >
            Join the exclusive waitlist and be among the first to revolutionize your investment analysis workflow.
          </motion.p>
          
          <motion.div variants={fadeInUp}>
            {!isSubmitted ? (
              <form onSubmit={handleWaitlistSubmit} className="flex justify-center">
                <div className="flex flex-col sm:flex-row gap-4 max-w-lg">
                  <div className="flex-1">
                    <Input
                      type="email"
                      placeholder="Enter your email for early access"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="bg-white/10 border-white/20 text-white placeholder:text-white/70 h-12 text-lg backdrop-blur-sm"
                      required
                    />
                  </div>
                  <Button 
                    type="submit"
                    size="lg" 
                    className="bg-white text-blue-600 hover:bg-blue-50 px-8 h-12 text-lg font-semibold"
                    disabled={isLoading}
                  >
                    {isLoading ? "Adding..." : "Join Waitlist"}
                    <Mail className="ml-2 h-5 w-5" />
                  </Button>
                </div>
              </form>
            ) : (
              <div className="bg-white/10 border border-white/20 rounded-lg p-6 max-w-md mx-auto backdrop-blur-sm">
                <div className="flex items-center justify-center gap-3">
                  <CheckCircle className="h-6 w-6 text-white" />
                  <div className="text-white">
                    <p className="font-semibold">Welcome to the waitlist!</p>
                    <p className="text-sm opacity-90">We'll be in touch soon.</p>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </motion.section>
    </div>
  );
};

export default Index;