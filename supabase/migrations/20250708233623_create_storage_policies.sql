-- Create storage policies for the documents bucket
-- This allows the service role to access files for processing

-- Policy for service role to read files (this is the key one we need)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'objects' 
        AND schemaname = 'storage' 
        AND policyname = 'Service role can read documents'
    ) THEN
        CREATE POLICY "Service role can read documents" 
        ON storage.objects 
        FOR SELECT 
        USING (bucket_id = 'documents');
    END IF;
END $$; 