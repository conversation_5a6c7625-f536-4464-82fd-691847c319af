import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'https://esm.sh/docx@8.5.0';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const claudeApiKey = Deno.env.get('ANTHROPIC_API_KEY');

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Check if API key is set
    if (!claude<PERSON><PERSON><PERSON>ey) {
      throw new Error('ANTHROPIC_API_KEY environment variable is not set');
    }
    
    const { userId } = await req.json();

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Fetch user's files
    console.log(`\n=== FETCHING FILES FOR USER ${userId} ===`);
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('*')
      .eq('user_id', userId);

    if (filesError) {
      console.error('Error fetching files:', filesError);
      throw filesError;
    }

    console.log(`Found ${files?.length || 0} files for user`);

    if (!files || files.length === 0) {
      throw new Error('No files found for analysis');
    }

    // Log file details
    files.forEach(file => {
      console.log(`File: ${file.name}, Type: ${file.file_type}, Path: ${file.file_path}, Size: ${file.file_size}`);
    });

    // Group files by folder
    const filesByFolder = files.reduce((acc, file) => {
      if (!acc[file.folder_id]) {
        acc[file.folder_id] = [];
      }
      acc[file.folder_id].push(file);
      return acc;
    }, {} as Record<string, any[]>);

    // Extract content from all files
    const extractedContents: Record<string, string> = {};
    
    console.log(`Processing ${files.length} files for user ${userId}`);
    
    for (const file of files) {
      try {
        console.log(`\n=== Processing file: ${file.name} ===`);
        console.log(`File ID: ${file.id}`);
        console.log(`File path: ${file.file_path}`);
        console.log(`File type: ${file.file_type}`);
        console.log(`File size: ${file.file_size}`);
        
        // Download file directly from storage using service role
        console.log(`Attempting to download file from storage...`);
        
        // Download file from storage
        const { data: fileData, error: downloadError } = await supabase.storage
          .from('documents')
          .download(file.file_path);
          
        if (downloadError) {
          console.error(`Failed to download file ${file.name}:`, downloadError);
          extractedContents[file.id] = `[Content extraction failed for ${file.name}: ${downloadError.message}]`;
          continue;
        }
        
        console.log(`Successfully downloaded file ${file.name}`);
        
        // Convert file to buffer
        const fileBuffer = await fileData.arrayBuffer();
        console.log(`File buffer size: ${fileBuffer.byteLength} bytes`);
        
        // Call the extract-documents function with file buffer
        console.log(`Calling extract-documents function...`);
        const extractResponse = await fetch(`${supabaseUrl}/functions/v1/extract-documents`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${supabaseKey}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            fileBuffer: Array.from(new Uint8Array(fileBuffer)),
            fileType: file.file_type,
            fileName: file.name
          }),
        });

        if (extractResponse.ok) {
          const extractResult = await extractResponse.json();
          console.log(`Extract response for ${file.name}:`, JSON.stringify(extractResult, null, 2));
          if (extractResult.success) {
            extractedContents[file.id] = extractResult.text;
            console.log(`Successfully extracted content from: ${file.name} (${extractResult.text.length} characters)`);
          } else {
            console.error(`Failed to extract content from ${file.name}:`, extractResult.error);
            extractedContents[file.id] = `[Content extraction failed for ${file.name}: ${extractResult.error}]`;
          }
        } else {
          const errorText = await extractResponse.text();
          console.error(`Extract API call failed for ${file.name}:`, extractResponse.status, errorText);
          extractedContents[file.id] = `[Content extraction failed for ${file.name}: HTTP ${extractResponse.status}]`;
        }
      } catch (error) {
        console.error(`Error extracting content from ${file.name}:`, error);
        extractedContents[file.id] = `[Content extraction failed for ${file.name}]`;
      }
    }

    // Create analysis prompt based on uploaded files with extracted content
    const analysisPrompt = createAnalysisPrompt(filesByFolder, extractedContents);

    // Debug: Log the extracted contents and check total size
    console.log('\n=== EXTRACTED CONTENTS DEBUG ===');
    let totalContentSize = 0;
    Object.entries(extractedContents).forEach(([fileId, content]) => {
      const file = files.find(f => f.id === fileId);
      console.log(`File: ${file?.name || 'Unknown'}`);
      console.log(`Content length: ${content.length}`);
      console.log(`Content preview: ${content.substring(0, 200)}...`);
      totalContentSize += content.length;
      console.log('---');
    });
    
    console.log(`Total content size: ${totalContentSize} characters (${(totalContentSize / 1024 / 1024).toFixed(2)} MB)`);
    
    // Check if content is too large for Claude API (8MB limit)
    const MAX_PROMPT_SIZE = 8 * 1024 * 1024; // 8MB in characters
    let finalPrompt = analysisPrompt;
    if (analysisPrompt.length > MAX_PROMPT_SIZE) {
      console.warn(`Prompt too large (${analysisPrompt.length} chars), truncating to fit Claude API limits`);
      // Truncate the prompt while keeping the structure
      finalPrompt = analysisPrompt.substring(0, MAX_PROMPT_SIZE - 1000) + '\n\n[Content truncated due to size limits]';
      console.log(`Truncated prompt size: ${finalPrompt.length} characters`);
    }

    // Generate investment memo using Claude with retry logic
    const maxRetries = 3;
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempting Claude API call (attempt ${attempt}/${maxRetries})`);
        
        const response = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: {
            'x-api-key': `${claudeApiKey}`,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01',
          },
          body: JSON.stringify({
            model: 'claude-3-5-sonnet-20241022',
            max_tokens: 4000,
            temperature: 0.1,
            system: `You are an expert real estate investment analyst. Generate a comprehensive, professional investment memorandum based on the uploaded documents. The memo should be detailed, well-structured, and suitable for institutional investors.`,
            messages: [
              {
                role: 'user',
                content: finalPrompt
              }
            ]
          }),
        });

        if (response.ok) {
          console.log(`Claude API call successful on attempt ${attempt}`);
          const aiResponse = await response.json();
          const memoContent = aiResponse.content[0].text;

          // Generate Word document
          const wordDoc = await generateWordDocument(memoContent);

          return new Response(
            JSON.stringify({ documentBuffer: Array.from(wordDoc) }),
            {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            }
          );
        } else {
          const errorText = await response.text();
          console.error(`Claude API error (attempt ${attempt}):`, response.status, errorText);
          
          // If it's a 529 overload error, wait and retry
          if (response.status === 529) {
            lastError = new Error(`Claude servers are overloaded (attempt ${attempt}/${maxRetries}): ${response.status} - ${errorText}`);
            if (attempt < maxRetries) {
              const waitTime = Math.pow(2, attempt) * 1000; // Exponential backoff: 2s, 4s, 8s
              console.log(`Waiting ${waitTime}ms before retry...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
              continue;
            }
          } else {
            // For other errors, don't retry
            throw new Error(`Failed to generate memo with Claude: ${response.status} - ${errorText}`);
          }
        }
      } catch (error) {
        console.error(`Error on attempt ${attempt}:`, error);
        lastError = error;
        
        if (attempt < maxRetries) {
          const waitTime = Math.pow(2, attempt) * 1000;
          console.log(`Waiting ${waitTime}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }
    
    // If we get here, all retries failed
    throw lastError || new Error('All retry attempts failed');

  } catch (error) {
    console.error('Error in generate-investment-memo function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});

function createAnalysisPrompt(filesByFolder: Record<string, any[]>, extractedContents: Record<string, string>): string {
  let prompt = `Please analyze the following real estate investment opportunity based on the uploaded documents and generate a comprehensive Investment Memorandum.

DOCUMENT INVENTORY AND CONTENT:
`;

  // Add file inventory by category with extracted content
  Object.entries(filesByFolder).forEach(([folderId, files]) => {
    const folderName = getFolderName(folderId);
    prompt += `\n${folderName.toUpperCase()} (${files.length} files):
`;
    files.forEach(file => {
      prompt += `\n--- ${file.name} (${file.file_type}) ---\n`;
      const content = extractedContents[file.id];
      if (content && content !== `[Content extraction failed for ${file.name}]`) {
        prompt += content + '\n';
      } else {
        prompt += `[Content extraction failed or unavailable for ${file.name}]\n`;
      }
      prompt += `--- End of ${file.name} ---\n`;
    });
  });

  prompt += `

INVESTMENT MEMORANDUM REQUIREMENTS:

Please generate a comprehensive investment memorandum with the following structure:

1. EXECUTIVE SUMMARY
   - Investment opportunity overview
   - Key investment highlights
   - Financial summary
   - Risk assessment summary
   - Investment recommendation

2. PROPERTY OVERVIEW
   - Property description and location
   - Physical characteristics
   - Current condition and improvements needed
   - Zoning and land use

3. MARKET ANALYSIS
   - Local market conditions
   - Comparable properties analysis
   - Market trends and projections
   - Competitive landscape

4. FINANCIAL ANALYSIS
   - Purchase price and acquisition costs
   - Operating income and expenses
   - Cash flow projections
   - Return metrics (IRR, NPV, Cap Rate, etc.)
   - Sensitivity analysis

5. INVESTMENT STRATEGY
   - Hold period and exit strategy
   - Value creation opportunities
   - Capital improvements plan
   - Financing structure

6. RISK ANALYSIS
   - Market risks
   - Property-specific risks
   - Financial risks
   - Mitigation strategies

7. CONCLUSION AND RECOMMENDATION
   - Investment thesis
   - Expected returns
   - Risk-adjusted recommendation

Please provide detailed analysis based on the document types available. If certain information is not available from the uploaded documents, please note what additional information would be needed for a complete analysis.

Make the memo professional, comprehensive, and suitable for presentation to institutional investors or investment committees.
`;

  return prompt;
}

function getFolderName(folderId: string): string {
  const folderNames: Record<string, string> = {
    'financial': 'Financial Documents',
    'property': 'Property Analysis',
    'market': 'Market Research',
    'legal': 'Legal Documents'
  };
  return folderNames[folderId] || folderId;
}

async function generateWordDocument(content: string): Promise<Uint8Array> {
  // Parse the content and create structured document sections
  const sections = parseContentSections(content);
  
  const doc = new Document({
    sections: [{
      properties: {},
      children: [
        // Title
        new Paragraph({
          children: [
            new TextRun({
              text: "INVESTMENT MEMORANDUM",
              bold: true,
              size: 32,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 400 },
        }),
        
        new Paragraph({
          children: [
            new TextRun({
              text: `Generated on ${new Date().toLocaleDateString()}`,
              italics: true,
              size: 20,
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: { after: 800 },
        }),
        
        ...sections.flatMap(section => [
          new Paragraph({
            children: [
              new TextRun({
                text: section.title,
                bold: true,
                size: 24,
              }),
            ],
            heading: HeadingLevel.HEADING_1,
            spacing: { before: 400, after: 200 },
          }),
          ...section.content.map(paragraph => 
            new Paragraph({
              children: [
                new TextRun({
                  text: paragraph,
                  size: 22,
                }),
              ],
              spacing: { after: 200 },
            })
          ),
        ]),
      ],
    }],
  });

  return await Packer.toBuffer(doc);
}

function parseContentSections(content: string): { title: string; content: string[] }[] {
  const sections: { title: string; content: string[] }[] = [];
  const lines = content.split('\n').filter(line => line.trim());
  
  let currentSection: { title: string; content: string[] } | null = null;
  
  for (const line of lines) {
    // Check if line is a section header (starts with number or all caps)
    if (line.match(/^\d+\.\s+[A-Z\s]+$/) || line.match(/^[A-Z\s]{3,}:?\s*$/)) {
      if (currentSection) {
        sections.push(currentSection);
      }
      currentSection = {
        title: line.replace(/^\d+\.\s*/, '').replace(':', '').trim(),
        content: []
      };
    } else if (currentSection && line.trim()) {
      currentSection.content.push(line.trim());
    } else if (!currentSection) {
      // Handle content before first section
      if (!sections.length) {
        sections.push({ title: 'Executive Summary', content: [] });
        currentSection = sections[0];
      }
      if (line.trim()) {
        currentSection.content.push(line.trim());
      }
    }
  }
  
  if (currentSection && currentSection.content.length > 0) {
    sections.push(currentSection);
  }
  
  return sections.length ? sections : [{ title: 'Investment Analysis', content: lines }];
}