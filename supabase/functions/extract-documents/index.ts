// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Maximum content size to prevent Claude API errors (8MB to be safe)
const MAX_CONTENT_SIZE = 8 * 1024 * 1024; // 8MB in bytes

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { fileBuffer, fileType, fileName } = await req.json();
    if (!fileBuffer) {
      throw new Error('File buffer is required');
    }
    
    const ext = fileName.split('.').pop().toLowerCase();
    const buffer = new Uint8Array(fileBuffer);
    let extractedText = '';
    
    try {
      if (ext === 'txt') {
        // Simple text file extraction
        extractedText = new TextDecoder().decode(buffer);
        console.log(`Extracted ${extractedText.length} characters from text file`);
      } else if (ext === 'pdf') {
        // For PDF, we'll use a simple approach that works in Deno
        // This is a basic fallback - you may want to use a Deno-compatible PDF library
        extractedText = '[PDF content extraction - using basic text extraction]';
        // Try to extract any text content from the buffer
        const textDecoder = new TextDecoder('utf-8', { fatal: false });
        const textContent = textDecoder.decode(buffer);
        if (textContent && textContent.trim().length > 0) {
          // Only take the first 100KB of text content to avoid massive data
          extractedText = textContent.substring(0, 100000);
          console.log(`Extracted ${extractedText.length} characters from PDF (truncated)`);
        } else {
          extractedText = '[PDF content could not be extracted as text]';
        }
      } else if (ext === 'docx') {
        // For DOCX, we'll use a simple approach
        extractedText = '[DOCX content extraction - using basic text extraction]';
        // Try to extract any text content from the buffer
        const textDecoder = new TextDecoder('utf-8', { fatal: false });
        const textContent = textDecoder.decode(buffer);
        if (textContent && textContent.trim().length > 0) {
          // Only take the first 100KB of text content
          extractedText = textContent.substring(0, 100000);
          console.log(`Extracted ${extractedText.length} characters from DOCX (truncated)`);
        } else {
          extractedText = '[DOCX content could not be extracted as text]';
        }
      } else if (ext === 'xlsx' || ext === 'xls' || ext === 'xlsm') {
        // For Excel, we'll use a simple approach
        extractedText = '[Excel content extraction - using basic text extraction]';
        // Try to extract any text content from the buffer
        const textDecoder = new TextDecoder('utf-8', { fatal: false });
        const textContent = textDecoder.decode(buffer);
        if (textContent && textContent.trim().length > 0) {
          // Only take the first 100KB of text content
          extractedText = textContent.substring(0, 100000);
          console.log(`Extracted ${extractedText.length} characters from Excel (truncated)`);
        } else {
          extractedText = '[Excel content could not be extracted as text]';
        }
      } else {
        extractedText = '[Unsupported file type]';
      }
      
      // Final safety check - ensure we don't exceed Claude's limits
      if (extractedText.length > MAX_CONTENT_SIZE) {
        extractedText = extractedText.substring(0, MAX_CONTENT_SIZE);
        console.log(`Content truncated to ${MAX_CONTENT_SIZE} characters to fit Claude API limits`);
      }
      
      console.log(`Final extracted content size: ${extractedText.length} characters`);
      
      return new Response(
        JSON.stringify({ 
          success: true, 
          text: extractedText,
          fileName: fileName,
          fileType: fileType,
          contentSize: extractedText.length
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    } catch (e) {
      console.error('Extraction error:', e);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Extraction failed', 
          details: e.message, 
          fileName 
        }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
  } catch (error) {
    console.error('Request error:', error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
