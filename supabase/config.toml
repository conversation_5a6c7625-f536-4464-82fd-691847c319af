project_id = "iunowowqpjuzwfmsserk"
[functions.extract-documents]
enabled = true
verify_jwt = true
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/extract-documents/index.ts"
# Specifies static files to be bundled with the function. Supports glob patterns.
# For example, if you want to serve static HTML pages in your function:
# static_files = [ "./functions/extract-documents/*.html" ]

[functions.generate-investment-memo]
enabled = true
verify_jwt = true
# Uncomment to specify a custom file path to the entrypoint.
# Supported file extensions are: .ts, .js, .mjs, .jsx, .tsx
entrypoint = "./functions/generate-investment-memo/index.ts"
